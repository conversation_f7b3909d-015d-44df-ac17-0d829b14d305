"use client";

import React, { useEffect, useState } from 'react';
import Link from 'next/link';
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import {
  Users,
  Search,
  Shield,
  UserCheck,
  Settings,
  Eye,
  Filter,
  MoreHorizontal,
  Mail,
  Building,
  Crown,
  Star,
  AlertCircle,
  CheckCircle,
  Loader2,
  Plus,
  Edit,
  Trash2,
  UserPlus,
  ShieldCheck,
  Lock,
  Unlock,
  Globe,
  BarChart3,
  DollarSign,
  Receipt,
  RefreshCw,
  TrendingUp,
  X
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import { fetchUsersWithRoles, getAllUniquePermissions, getAllUniqueGroups, getRoleStatistics } from "@/lib/api/roles/service";
import { UserWithRoleDetails, UsersWithRolesResponse, UserGroupInfo, PermissionCategory } from "@/lib/api/roles/models";
import Cookies from 'js-cookie';
import { toast } from "sonner";

// Permission categories with icons and colors
const permissionCategories: PermissionCategory[] = [
  {
    name: "Sales & Payments",
    permissions: ["Process Sales", "Create Invoices", "Issue Refunds", "Handle Returns", "Process Exchanges"],
    icon: "DollarSign",
    color: "bg-green-100 text-green-700 border-green-200"
  },
  {
    name: "Customer Management",
    permissions: ["Add Customers", "Edit Customers"],
    icon: "UserPlus",
    color: "bg-blue-100 text-blue-700 border-blue-200"
  },
  {
    name: "Product & Inventory",
    permissions: ["View Products", "Manage Inventory"],
    icon: "Lock",
    color: "bg-purple-100 text-purple-700 border-purple-200"
  },
  {
    name: "Reports & Analytics",
    permissions: ["View Reports"],
    icon: "BarChart3",
    color: "bg-orange-100 text-orange-700 border-orange-200"
  },
  {
    name: "Multi-Store Access",
    permissions: ["Access Multiple Stores"],
    icon: "Globe",
    color: "bg-indigo-100 text-indigo-700 border-indigo-200"
  }
];

// Role color mapping
const roleColors: { [key: string]: string } = {
  "Admin": "bg-red-100 text-red-700 border-red-200",
  "Manager": "bg-yellow-100 text-yellow-700 border-yellow-200",
  "Staff": "bg-green-100 text-green-700 border-green-200",
  "Cashier": "bg-blue-100 text-blue-700 border-blue-200",
  "Default": "bg-gray-100 text-gray-700 border-gray-200"
};

// Get user initials for avatar
const getUserInitials = (name: string): string => {
  return name
    .split(' ')
    .map(word => word.charAt(0))
    .join('')
    .toUpperCase()
    .slice(0, 2);
};

// User Detail Dialog Component
interface UserDetailDialogProps {
  user: UserWithRoleDetails | null;
  isOpen: boolean;
  onClose: () => void;
  router: any;
}

function UserDetailDialog({ user, isOpen, onClose, router }: UserDetailDialogProps) {
  if (!user) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl w-[100vw] h-[100vh] sm:w-[95vw] sm:h-[90vh] sm:max-h-[90vh] sm:rounded-lg flex flex-col p-0 overflow-hidden">
        <DialogHeader className="flex-shrink-0 p-3 sm:p-6 border-b bg-background">
          <div className="flex items-center justify-between">
            <DialogTitle className="text-lg sm:text-xl font-semibold flex items-center">
              <UserCheck className="mr-2 h-4 w-4 sm:h-5 sm:w-5 text-primary" />
              User Details
            </DialogTitle>
          </div>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto p-3 sm:p-6">
          {/* User Info */}
          <div className="flex flex-col sm:flex-row sm:items-start space-y-4 sm:space-y-0 sm:space-x-4 mb-6">
            <Avatar className="h-12 w-12 sm:h-16 sm:w-16 mx-auto sm:mx-0">
              <AvatarFallback className="bg-primary/10 text-primary font-semibold text-sm sm:text-lg">
                {getUserInitials(user.name)}
              </AvatarFallback>
            </Avatar>
            <div className="flex-1 min-w-0 text-center sm:text-left">
              <div className="flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-2 mb-2">
                <h2 className="text-xl sm:text-2xl font-bold text-foreground">{user.name}</h2>
                <Badge className={`${roleColors[user.user_defined_role] || roleColors.Default} mx-auto sm:mx-0`}>
                  {user.user_defined_role}
                </Badge>
              </div>
              <div className="space-y-2">
                <div className="flex items-center justify-center sm:justify-start space-x-2 text-muted-foreground">
                  <Mail className="h-4 w-4" />
                  <span className="text-sm sm:text-base break-all">{user.email}</span>
                </div>
                <div className="flex items-center justify-center sm:justify-start space-x-2 text-muted-foreground">
                  <Building className="h-4 w-4" />
                  <span className="text-sm sm:text-base">{user.store_name}</span>
                </div>
              </div>
            </div>
          </div>

          <Separator className="my-6" />

          {/* Permission Groups */}
          {user.groups.length > 0 && (
            <div className="mb-6">
              <h3 className="text-base sm:text-lg font-semibold mb-4 flex items-center justify-center sm:justify-start">
                <Shield className="h-4 w-4 sm:h-5 sm:w-5 mr-2 text-primary" />
                Permission Groups ({user.groups.length})
              </h3>
              <div className="grid gap-3 sm:gap-4">
                {user.groups.map((group) => (
                  <Card key={group.group_id} className="p-3 sm:p-4">
                    <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between space-y-2 sm:space-y-0 mb-2">
                      <h4 className="font-medium text-foreground text-center sm:text-left">{group.group_name}</h4>
                      <Badge variant="secondary" className="mx-auto sm:mx-0 w-fit">
                        {group.permissions.length} permissions
                      </Badge>
                    </div>
                    {group.group_description && (
                      <p className="text-xs sm:text-sm text-muted-foreground mb-3 text-center sm:text-left">
                        {group.group_description}
                      </p>
                    )}
                    <div className="flex flex-wrap gap-1 justify-center sm:justify-start">
                      {group.permissions.map((permission) => (
                        <Badge key={permission} variant="outline" className="text-xs">
                          {permission}
                        </Badge>
                      ))}
                    </div>
                  </Card>
                ))}
              </div>
            </div>
          )}

          {/* All Permissions */}
          <div className="mb-4 sm:mb-6">
            <h3 className="text-base sm:text-lg font-semibold mb-4 flex items-center justify-center sm:justify-start">
              <Lock className="h-4 w-4 sm:h-5 sm:w-5 mr-2 text-primary" />
              All Permissions ({user.all_permissions.length})
            </h3>
            {user.all_permissions.length > 0 ? (
              <div className="flex flex-wrap gap-1 sm:gap-2 justify-center sm:justify-start">
                {user.all_permissions.map((permission) => (
                  <Badge key={permission} variant="secondary" className="text-xs sm:text-sm">
                    {permission}
                  </Badge>
                ))}
              </div>
            ) : (
              <div className="text-center py-6 sm:py-8">
                <AlertCircle className="h-8 w-8 sm:h-12 sm:w-12 text-muted-foreground mx-auto mb-2 sm:mb-4" />
                <p className="text-sm sm:text-base text-muted-foreground">No permissions assigned to this user</p>
              </div>
            )}
          </div>
        </div>

        {/* Footer - Mobile-optimized */}
        <div className="flex-shrink-0 p-3 sm:p-6 border-t bg-background">
          <div className="flex flex-col sm:flex-row gap-2 sm:gap-0 sm:justify-end sm:space-x-2">
            <Button
              onClick={() => {
                onClose();
                router.push(`/app/users/${user.user_id}/edit`);
              }}
              className="w-full sm:w-auto"
            >
              <Edit className="h-4 w-4 mr-2" />
              Edit User
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

const UsersRolesPermissionsPage = () => {
  const router = useRouter();
  const [usersData, setUsersData] = useState<UsersWithRolesResponse | null>(null);
  const [filteredUsers, setFilteredUsers] = useState<UserWithRoleDetails[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedRole, setSelectedRole] = useState<string>('all');
  const [selectedUser, setSelectedUser] = useState<UserWithRoleDetails | null>(null);
  const [isDetailDialogOpen, setIsDetailDialogOpen] = useState(false);

  // Get store ID from cookies or context (you might need to adjust this)
  const getStoreId = (): string => {
    // For now, using the store ID from your example
    // In a real app, this would come from user context or cookies
    return "2593deee-2bfd-46e3-9e9c-64cd91ea5c04";
  };

  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      setError(null);
      try {
        const token = Cookies.get("auth_token");
        const storeId = getStoreId();

        if (!token) {
          setError(new Error("Authentication token not found. Please log in again."));
          setIsLoading(false);
          return;
        }

        const response = await fetchUsersWithRoles(storeId, token);
        setUsersData(response);
        setFilteredUsers(response.users);
      } catch (err: any) {
        setError(err instanceof Error ? err : new Error(err.message || "Failed to fetch users and roles."));
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  // Filter users based on search term and selected role
  useEffect(() => {
    if (!usersData) return;

    let filtered = usersData.users;

    // Filter by search term
    if (searchTerm) {
      const lowerCaseSearchTerm = searchTerm.toLowerCase();
      filtered = filtered.filter(user =>
        user.name.toLowerCase().includes(lowerCaseSearchTerm) ||
        user.email.toLowerCase().includes(lowerCaseSearchTerm) ||
        user.user_defined_role.toLowerCase().includes(lowerCaseSearchTerm)
      );
    }

    // Filter by role
    if (selectedRole !== 'all') {
      filtered = filtered.filter(user => user.user_defined_role === selectedRole);
    }

    setFilteredUsers(filtered);
  }, [searchTerm, selectedRole, usersData]);

  // Function to handle user detail view
  const handleUserDetail = (user: UserWithRoleDetails) => {
    setSelectedUser(user);
    setIsDetailDialogOpen(true);
  };

  // Function to handle user edit
  const handleUserEdit = (userId: string) => {
    router.push(`/app/users/${userId}/edit`);
  };

  // Function to handle user delete
  const handleUserDelete = (userId: string) => {
    // Add delete functionality here
    console.log('Delete user:', userId);
    toast.error('Delete functionality not implemented yet');
  };

  // Get unique roles for filter dropdown
  const uniqueRoles = usersData ? Array.from(new Set(usersData.users.map(user => user.user_defined_role))) : [];

  if (isLoading) {
    return (
      <div className="min-h-screen flex justify-center items-center bg-background">
        <div className="flex flex-col items-center space-y-4">
          <Loader2 className="h-12 w-12 animate-spin text-primary" />
          <p className="text-muted-foreground font-medium">Loading users, roles and permissions...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex justify-center items-center bg-background p-4">
        <Card className="max-w-md w-full">
          <CardContent className="pt-6">
            <div className="text-destructive flex items-center justify-center mb-4">
              <AlertCircle className="h-12 w-12" />
            </div>
            <h2 className="text-xl font-bold text-center mb-2 text-foreground">Error Loading Data</h2>
            <p className="text-muted-foreground text-center">{error.message}</p>
            <div className="mt-6 flex justify-center">
              <Button variant="outline" asChild>
                <Link href="/app/users">
                  Return to Users
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="bg-background min-h-screen">
      {/* Breadcrumb */}
      <div className="bg-card border-b">
        <div className="container mx-auto px-3 sm:px-4 py-3">
          <div className="flex text-xs sm:text-sm text-muted-foreground items-center flex-wrap">
            <Link href="/app/dashboard" className="hover:text-primary">Dashboard</Link>
            <span className="mx-1 sm:mx-2">/</span>
            <Link href="/app/users" className="hover:text-primary">Users</Link>
            <span className="mx-1 sm:mx-2">/</span>
            <span className="text-foreground font-medium">Roles & Permissions</span>
          </div>
        </div>
      </div>

      <div className="container mx-auto p-3 sm:p-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold text-foreground">Users & Permissions</h1>
            <p className="text-muted-foreground mt-1">
              Manage user roles, groups, and permissions for {usersData?.users[0]?.store_name || 'your store'}
            </p>
          </div>
          <div className="flex gap-2 mt-4 sm:mt-0">
            <Button variant="outline" onClick={() => window.location.reload()}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
            <Button onClick={() => router.push('/app/users/create')}>
              <UserPlus className="h-4 w-4 mr-2" />
              Add User
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        {usersData && (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <Users className="h-8 w-8 text-blue-600" />
                  <div>
                    <p className="text-2xl font-bold text-foreground">{usersData.total_users}</p>
                    <p className="text-sm text-muted-foreground">Total Users</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <Shield className="h-8 w-8 text-green-600" />
                  <div>
                    <p className="text-2xl font-bold text-foreground">{uniqueRoles.length}</p>
                    <p className="text-sm text-muted-foreground">Active Roles</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <ShieldCheck className="h-8 w-8 text-purple-600" />
                  <div>
                    <p className="text-2xl font-bold text-foreground">{getAllUniqueGroups(usersData.users).length}</p>
                    <p className="text-sm text-muted-foreground">Permission Groups</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <Lock className="h-8 w-8 text-orange-600" />
                  <div>
                    <p className="text-2xl font-bold text-foreground">{getAllUniquePermissions(usersData.users).length}</p>
                    <p className="text-sm text-muted-foreground">Permissions</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Search and Filters */}
        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              type="text"
              placeholder="Search users by name, email, or role..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <div className="flex gap-2">
            <select
              value={selectedRole}
              onChange={(e) => setSelectedRole(e.target.value)}
              className="px-3 py-2 border border-input bg-background rounded-md text-sm"
            >
              <option value="all">All Roles</option>
              {uniqueRoles.map(role => (
                <option key={role} value={role}>{role}</option>
              ))}
            </select>
          </div>
        </div>

        {/* Users List */}
        <div className="space-y-4">
          {filteredUsers.length > 0 ? (
            filteredUsers.map((user) => (
              <Card
                key={user.user_id}
                className="p-4 sm:p-6 hover:shadow-md transition-shadow cursor-pointer"
                onClick={() => handleUserDetail(user)}
              >
                <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between space-y-4 sm:space-y-0">
                  {/* User Info */}
                  <div className="flex items-start space-x-4">
                    <Avatar className="h-12 w-12">
                      <AvatarFallback className="bg-primary/10 text-primary font-semibold">
                        {getUserInitials(user.name)}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2 mb-1">
                        <h3 className="text-lg font-semibold text-foreground">{user.name}</h3>
                        <Badge className={roleColors[user.user_defined_role] || roleColors.Default}>
                          {user.user_defined_role}
                        </Badge>
                      </div>
                      <div className="flex items-center space-x-2 text-sm text-muted-foreground mb-2">
                        <Mail className="h-4 w-4" />
                        <span>{user.email}</span>
                      </div>
                      <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                        <Building className="h-4 w-4" />
                        <span>{user.store_name}</span>
                      </div>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleUserDetail(user);
                      }}
                    >
                      <Eye className="h-4 w-4 mr-2" />
                      View Details
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleUserEdit(user.user_id);
                      }}
                    >
                      <Edit className="h-4 w-4 mr-2" />
                      Edit
                    </Button>
                  </div>
                </div>

                {/* Groups and Permissions */}
                <div className="mt-4 space-y-4">
                  {/* Groups */}
                  {user.groups.length > 0 && (
                    <div>
                      <h4 className="text-sm font-medium text-foreground mb-2">Permission Groups</h4>
                      <div className="flex flex-wrap gap-2">
                        {user.groups.map((group) => (
                          <Badge key={group.group_id} variant="secondary" className="text-xs">
                            {group.group_name}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* All Permissions */}
                  {user.all_permissions.length > 0 && (
                    <div>
                      <h4 className="text-sm font-medium text-foreground mb-2">
                        All Permissions ({user.all_permissions.length})
                      </h4>
                      <div className="flex flex-wrap gap-1">
                        {user.all_permissions.slice(0, 6).map((permission) => (
                          <Badge key={permission} variant="outline" className="text-xs">
                            {permission}
                          </Badge>
                        ))}
                        {user.all_permissions.length > 6 && (
                          <Badge variant="outline" className="text-xs">
                            +{user.all_permissions.length - 6} more
                          </Badge>
                        )}
                      </div>
                    </div>
                  )}

                  {user.groups.length === 0 && user.all_permissions.length === 0 && (
                    <div className="text-center py-4">
                      <AlertCircle className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                      <p className="text-sm text-muted-foreground">No permissions assigned</p>
                    </div>
                  )}
                </div>
              </Card>
            ))
          ) : (
            <Card className="p-8">
              <div className="text-center">
                <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-medium text-foreground mb-2">
                  {usersData?.users.length === 0 ? "No Users Found" : "No Users Match Your Search"}
                </h3>
                <p className="text-muted-foreground">
                  {usersData?.users.length === 0
                    ? "Start by adding users to your store."
                    : "Try adjusting your search criteria or filters."}
                </p>
                {usersData?.users.length === 0 && (
                  <Button className="mt-4" onClick={() => router.push('/app/users/create')}>
                    <UserPlus className="h-4 w-4 mr-2" />
                    Add First User
                  </Button>
                )}
              </div>
            </Card>
          )}
        </div>
      </div>

      {/* User Detail Dialog */}
      <UserDetailDialog
        user={selectedUser}
        isOpen={isDetailDialogOpen}
        onClose={() => {
          setIsDetailDialogOpen(false);
          setSelectedUser(null);
        }}
        router={router}
      />
    </div>
  );
};

export default UsersRolesPermissionsPage;