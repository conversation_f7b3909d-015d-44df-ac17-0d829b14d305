export interface SellItemSchema {
  item_uuid: string;
  price: number;
  quantity: number;
  store_id: string;
  has_vat?: boolean;
  vat_percentage?: number;
}

export interface MpesaPayload {
  amount: number;
  phone_number: string;
}

export type PaymentMethod = "mpesa" | "card" | "cash";

export interface SellItemsSchema {
  items: SellItemSchema[];
  mpesa_payload: MpesaPayload | null;
  payment_method: PaymentMethod | null;
}

export interface Sale {
  id: string,
  item_id: string,
  salesperson_id: string,
  sale_time: string,
  quantity: number,
  total: number,
  items_remaining: number,
  store_id: string,
  receipt_id: string,
  status: SaleStatus,
  date_updated?: string,
  has_vat?: boolean,
  vat_percentage?: number,
}

export enum SaleStatus {
  Completed = "completed",
  Returned = "returned",
  Pending = "pending",
  Canceled = "canceled",
}
