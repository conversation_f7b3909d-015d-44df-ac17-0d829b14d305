"use client";

import React, { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>er,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import {
  Attributes,
  AttributeValues,
  AttributeValuesSchema,
} from "@/lib/api/attributes/models";
import { Supplier } from "@/lib/api/suppliers/models";
import MatrixAttributes from "./create/AttributesSection";
import { Category } from "@/lib/api/categories/models";
import { Brand } from "@/lib/api/brands/models";
import { AttributeConfig } from "./create/AttributesSection";
import { ItemMatrixSchema } from "@/lib/api/items/matrices/models";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";

const matrixFormSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  matrix_type: z.string().min(1, "Matrix type is required"),
  description: z.string().optional(),
  category: z.string().optional(),
  brand: z.string().optional(),
  vendor_id: z.string().optional(),
  default_price: z.number().min(0, "Price cannot be negative"),
  default_cost: z.number().min(0, "Cost cannot be negative"),
  msrp: z.number().min(0, "MSRP cannot be negative").optional(),
  has_discount: z.boolean().default(false),
  discount: z.number().min(0, "Discount cannot be negative").max(100, "Discount cannot exceed 100%").optional(),
  vat_included: z.boolean().default(false),
  vat_percentage: z.number().min(0, "VAT cannot be negative").max(100, "VAT cannot exceed 100%").optional(),
});

type MatrixFormData = z.infer<typeof matrixFormSchema>;

interface MatrixFormProps {
  initialData?: ItemMatrixSchema;
  attributes: AttributeConfig[];
  availableAttributes: Attributes[];
  availableAttributeValues: AttributeValues[];
  vendors: Supplier[];
  categories: Category[];
  brands: Brand[];
  storeId: string;
  onSubmit: (data: ItemMatrixSchema, attributes: AttributeConfig[]) => Promise<void>;
  onCancel: () => void;
  onAddAttribute: (attributeId: string) => void;
  onRemoveAttribute: (attributeId: string) => void;
  onToggleValue: (attributeId: string, valueId: string) => void;
  onAddValue: (attributeId: string, value: string) => void;
  onRemoveValue: (attributeId: string, valueId: string) => void;
  onAddBulkValues: (attributeId: string, values: AttributeValuesSchema) => Promise<void>;
  loading: boolean;
  saving: boolean;
}

const matrixTypes = [
  { id: "size", name: "Size Based" },
  { id: "color", name: "Color Based" },
  { id: "combo", name: "Size & Color" },
];

export const MatrixForm: React.FC<MatrixFormProps> = ({
  initialData,
  attributes,
  availableAttributes,
  availableAttributeValues,
  vendors,
  categories,
  brands,
  storeId,
  onSubmit,
  onCancel,
  onAddAttribute,
  onRemoveAttribute,
  onToggleValue,
  onAddValue,
  onRemoveValue,
  onAddBulkValues,
  loading,
  saving
}) => {
  const form = useForm<MatrixFormData>({
    resolver: zodResolver(matrixFormSchema),
    defaultValues: {
      name: initialData?.name || "",
      matrix_type: initialData?.matrix_type || "",
      description: initialData?.description || "",
      category: initialData?.category || "",
      brand: initialData?.brand || "",
      vendor_id: initialData?.vendor_id || "",
      default_price: initialData?.default_price || 0,
      default_cost: initialData?.default_cost || 0,
      msrp: initialData?.msrp || 0,
      has_discount: initialData?.has_discount || false,
      discount: initialData?.discount || 0,
      vat_included: initialData?.vat_included || false,
      vat_percentage: initialData?.vat_percentage || 0,
    },
  });

  const handleSubmit = async (data: MatrixFormData) => {
    await onSubmit({
      ...data,
      store_id: storeId,
      description: data.description || "",
      category: data.category || "",
      brand: data.brand || "",
      vendor_id: data.vendor_id || "",
    }, attributes);
  };

  useEffect(() => {
    if (initialData) {
      form.reset({
        name: initialData.name,
        matrix_type: initialData.matrix_type,
        description: initialData.description || "",
        category: initialData.category || "",
        brand: initialData.brand || "",
        vendor_id: initialData.vendor_id || "",
        default_price: initialData.default_price,
        default_cost: initialData.default_cost,
        msrp: initialData.msrp || 0,
        has_discount: initialData.has_discount || false,
        discount: initialData.discount || 0,
        vat_included: initialData.vat_included || false,
        vat_percentage: initialData.vat_percentage || 0,
      });
    }
  }, [initialData, form]);

  return (
    <Card className="w-full mx-auto">
      <CardHeader>
        <CardTitle>{initialData ? "Edit Item Matrix" : "Create Item Matrix"}</CardTitle>
      </CardHeader>
      <form onSubmit={form.handleSubmit(handleSubmit)}>
        <CardContent className="space-y-6 p-4 md:p-6">
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Matrix Name *</Label>
                <Input
                  id="name"
                  {...form.register("name")}
                  placeholder="Enter matrix name"
                  className={form.formState.errors.name ? "border-red-500" : ""}
                />
                {form.formState.errors.name && (
                  <p className="text-sm text-red-500">{form.formState.errors.name.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="matrix_type">Matrix Type *</Label>
                <Select
                  value={form.watch("matrix_type")}
                  onValueChange={(value) => form.setValue("matrix_type", value)}
                >
                  <SelectTrigger id="matrix_type" className={form.formState.errors.matrix_type ? "border-red-500" : ""}>
                    <SelectValue placeholder="Select matrix type" />
                  </SelectTrigger>
                  <SelectContent>
                    {matrixTypes.map((type) => (
                      <SelectItem key={type.id} value={type.id}>
                        {type.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {form.formState.errors.matrix_type && (
                  <p className="text-sm text-red-500">{form.formState.errors.matrix_type.message}</p>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                {...form.register("description")}
                placeholder="Enter description"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="category">Category</Label>
              <Select
                value={form.watch("category")}
                onValueChange={(value) => form.setValue("category", value)}
              >
                <SelectTrigger id="category">
                  <SelectValue placeholder="Select category" />
                </SelectTrigger>
                <SelectContent>
                  {categories.map((category) => (
                    <SelectItem key={category.id} value={category.id}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="brand">Brand</Label>
              <Select
                value={form.watch("brand")}
                onValueChange={(value) => form.setValue("brand", value)}
              >
                <SelectTrigger id="brand">
                  <SelectValue placeholder="Select brand" />
                </SelectTrigger>
                <SelectContent>
                  {brands.map((brand) => (
                    <SelectItem key={brand.id} value={brand.id}>
                      {brand.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="vendor">Vendor</Label>
              <Select
                value={form.watch("vendor_id")}
                onValueChange={(value) => form.setValue("vendor_id", value)}
              >
                <SelectTrigger id="vendor">
                  <SelectValue placeholder="Select vendor" />
                </SelectTrigger>
                <SelectContent>
                  {vendors.map((vendor) => (
                    <SelectItem key={vendor.id} value={vendor.id}>
                      {vendor.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="default_price">Default Price *</Label>
              <Input
                id="default_price"
                type="number"
                step="0.01"
                min="0"
                {...form.register("default_price", { valueAsNumber: true })}
                placeholder="0.00"
                className={form.formState.errors.default_price ? "border-red-500" : ""}
              />
              {form.formState.errors.default_price && (
                <p className="text-sm text-red-500">{form.formState.errors.default_price.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="default_cost">Default Cost *</Label>
              <Input
                id="default_cost"
                type="number"
                step="0.01"
                min="0"
                {...form.register("default_cost", { valueAsNumber: true })}
                placeholder="0.00"
                className={form.formState.errors.default_cost ? "border-red-500" : ""}
              />
              {form.formState.errors.default_cost && (
                <p className="text-sm text-red-500">{form.formState.errors.default_cost.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="msrp">MSRP</Label>
              <Input
                id="msrp"
                type="number"
                step="0.01"
                min="0"
                {...form.register("msrp", { valueAsNumber: true })}
                placeholder="0.00"
                className={form.formState.errors.msrp ? "border-red-500" : ""}
              />
              {form.formState.errors.msrp && (
                <p className="text-sm text-red-500">{form.formState.errors.msrp.message}</p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 gap-x-8 gap-y-6">
            <div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="has_discount"
                  checked={form.watch("has_discount")}
                  onCheckedChange={(checked) => form.setValue("has_discount", checked)}
                />
                <Label htmlFor="has_discount">Enable Discount</Label>
              </div>

              {form.watch("has_discount") && (
                <div className="space-y-2 mt-2">
                  <Label htmlFor="discount">Discount Percentage</Label>
                  <Input
                    id="discount"
                    type="number"
                    step="0.1"
                    min="0"
                    max="100"
                    {...form.register("discount", { valueAsNumber: true })}
                    placeholder="0.0"
                    className={form.formState.errors.discount ? "border-red-500" : ""}
                  />
                  {form.formState.errors.discount && (
                    <p className="text-sm text-red-500">{form.formState.errors.discount.message}</p>
                  )}
                </div>
              )}
            </div>
            
            <div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="vat_included"
                  checked={form.watch("vat_included")}
                  onCheckedChange={(checked) => form.setValue("vat_included", checked)}
                />
                <Label htmlFor="vat_included">VAT Included</Label>
              </div>

              {form.watch("vat_included") && (
                <div className="space-y-2 mt-2">
                  <Label htmlFor="vat_percentage">VAT Percentage (%)</Label>
                  <Input
                    id="vat_percentage"
                    type="number"
                    step="0.01"
                    min="0"
                    max="100"
                    {...form.register("vat_percentage", { valueAsNumber: true })}
                    placeholder="0.00"
                    className={form.formState.errors.vat_percentage ? "border-red-500" : ""}
                  />
                  {form.formState.errors.vat_percentage && (
                    <p className="text-sm text-red-500">{form.formState.errors.vat_percentage.message}</p>
                  )}
                </div>
              )}
            </div>
          </div>
          
          <MatrixAttributes
            attributes={attributes}
            availableAttributes={availableAttributes}
            onAddAttribute={onAddAttribute}
            onRemoveAttribute={onRemoveAttribute}
            onToggleValue={onToggleValue}
            onAddValue={onAddValue}
            onRemoveValue={onRemoveValue}
            storeId={storeId}
            onAddBulkValues={onAddBulkValues}
          />
        </CardContent>

        <CardFooter className="flex justify-end space-x-2 p-4 md:p-6">
          <Button type="button" variant="outline" onClick={onCancel} disabled={saving}>
            Cancel
          </Button>
          <Button type="submit" disabled={saving}>
            {saving ? "Saving..." : initialData ? "Save Changes" : "Create Matrix"}
          </Button>
        </CardFooter>
      </form>
    </Card>
  );
};