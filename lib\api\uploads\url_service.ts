// pages/api/upload-url.ts (or app/api/upload-url/route.ts for App Router)

import { NextApiRequest, NextApiResponse } from 'next';

// For Pages Router (pages/api/upload-url.ts)
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { folder, filename, expires_in_seconds } = req.query;

    if (!folder || !filename) {
      return res.status(400).json({ error: 'Missing folder or filename' });
    }

    // Call your Rust backend
    const rustBackendUrl = process.env.RUST_BACKEND_URL || 'http://localhost:8080';
    const params = new URLSearchParams({
      folder: folder as string,
      filename: filename as string,
      ...(expires_in_seconds && { expires_in_seconds: expires_in_seconds as string }),
    });

    const response = await fetch(`${rustBackendUrl}/generate-upload-url?${params}`);
    
    if (!response.ok) {
      throw new Error(`Rust backend error: ${response.statusText}`);
    }

    const data = await response.json();
    res.status(200).json(data);

  } catch (error) {
    console.error('Error generating signed URL:', error);
    res.status(500).json({ 
      error: 'Failed to generate signed URL',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}

// For App Router (app/api/upload-url/route.ts)
export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const folder = searchParams.get('folder');
    const filename = searchParams.get('filename');
    const expires_in_seconds = searchParams.get('expires_in_seconds');

    if (!folder || !filename) {
      return Response.json({ error: 'Missing folder or filename' }, { status: 400 });
    }

    // Call your Rust backend
    const rustBackendUrl = process.env.RUST_BACKEND_URL || 'http://localhost:8080';
    const params = new URLSearchParams({
      folder,
      filename,
      ...(expires_in_seconds && { expires_in_seconds }),
    });

    const response = await fetch(`${rustBackendUrl}/generate-upload-url?${params}`);
    
    if (!response.ok) {
      throw new Error(`Rust backend error: ${response.statusText}`);
    }

    const data = await response.json();
    return Response.json(data);

  } catch (error) {
    console.error('Error generating signed URL:', error);
    return Response.json({ 
      error: 'Failed to generate signed URL',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}