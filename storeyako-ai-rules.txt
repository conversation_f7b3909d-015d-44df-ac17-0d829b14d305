Storeyako Frontend Issues and Enhancements.

1. Enable the store manager to switch user roles between manager and staff, and vice versa.
2. We currently have default permissions assigned to each user role, such as admin, manager, and user. For now, these settings should remain unchanged.
3. Proceed with integrating Paystack and enable a virtual terminal feature. This integration should initially be handled on the backend.
4. Is it possible to integrate WhatsApp and allow receipts to be sent through WhatsApp as well?
5. After a successful checkout, receipts should be printed immediately.
6. Error handling is essential and must be addressed. Although I have started working on it, the error handling is not yet consistent across all components. We also need to manage permissions properly.
7. When a user logs in and stores are loading, user information should also be loaded to determine their roles and decide which sidebar to display.
8. We use Firebase for uploading images via Firebase Storage. However, my Firebase rules currently do not allow verification of authenticated users, which poses a significant security risk. I prefer not to use my backend for this verification because it is built purely with Axum and Rust, not JavaScript or Dart. My authentication is custom-built from scratch, and my database is PostgreSQL, not Firebase.
9. A major refactoring is required regarding how errors are displayed.
10. Once all these tasks are completed, we need to use Capacitor to convert the project into an Android app.
11. A significant feature of the Android app should be offline functionality, allowing users to make sales without internet access. For offline mode, a checkout cart should be displayed that only supports cash sales, and temporary receipts should be issued.
12. When the user comes back online, the app should simulate the sales process. I will create a backend endpoint to sync offline sales, which will process the entire payload and handle each item individually, effectively 'selling' them. The UI should support this functionality.
13. Offline sales should be disabled if the inventory falls below 10 items. This threshold should be stored in the .env file for easy adjustment. The system is designed as a mobile POS, allowing each employee to act as a checkout operator using their phone, avoiding the need for expensive hardware. Additionally, I would like to include WhatsApp integration for sending receipts via WhatsApp or email if possible.
14. The dashboard's appearance is currently unsatisfactory. The system supports multi-store management, allowing users to manage multiple shops without creating new accounts. For example, a user might have shops in Kenya and Uganda. It would be logical to allow the user to select the currency for each store based on its country. However, at present, the dashboard displays combined figures for all shops using a single currency, which is not ideal. Users should be able to convert between currencies such as KES and UGX. This needs to be revised.

15. Vercel individual page analytics - I need to see where my users spend most of their time in the app.
16. When Adding Paystack integration, we must add where users will add their own secrets in `app/app/integrations/`. Just like I did it with mpesa.
18. I need to create blog or articles page. Where we can view blogs written. I will deploy headless CMS that will work with firebase.
We will then read the contents from firebase. We can leverage nextJS  serverside funcltionality + firebase for SEO, since we will give it pre-prendered content. So a simple blog or articles page, withv support for images, comments, SEO, meta fields etc.
19. We need to create also documentation. I will use headless CMS to generate content and our work will just be to display content. Kindly note that we will use firebase. SEO is a must.
20. Both blogs/articles and docs should be visible only in development for now. Notice this is not part of the core app.

21. Technologies and rules to follow:
    - Typescript
	- NextJS
	- Shadcn/ui combined with Tailwind CSS.

			
