"use client";
import * as React from "react";
import Image from "next/image";
import {
  Sidebar,
  SidebarContent,
  SidebarHeader,
  SidebarRail,
} from "@/components/ui/sidebar";

import {
  Receipt,
  Settings,
  Package,
  Users,
  FileText,
  BarChart2,
} from "lucide-react";

import { NavMain } from "@/components/nav-main";

const data = {
  navMain: [
    {
      title: "Inventory",
      url: "#",
      icon: Package,
      items: [
        {
          title: "Products",
          url: "/app/items",
        },
        // {
        //   title: "Stock Management",
        //   url: "#",
        // },
        {
          title: "Matrices",
          url: "/app/items/matrices",
        },
        {
          title: "Exchanges",
          url: "/app/items/exchanges",
        },
        {
          title: "Returns",
          url: "/app/items/returns",
        },
        {
          title: "Categories",
          url: "/app/items/categories",
        },
        {
          title: "Brands",
          url: "/app/items/brands",
        },
        {
          title: "Suppliers",
          url: "/app/suppliers",
        },
      ],
    },
    {
      title: "Sales",
      url: "/app/sales",
      icon: Receipt,
      items: [
        // {
        //   title: "New Sale",
        //   url: "#",
        // },
        {
          title: "Sales History",
          url: "/app/sales",
          isActive: true,
        },
        {
          title: "Transactions",
          url: "/app/sales/transactions",
          isActive: true,
        },
        // {
        //   title: "Discounts & Promotions",
        //   url: "#",
        // },
        {
          title: "Returns & Refunds",
          url: "/app/refunds",
        },
        {
          title: "Customer Management",
          url: "/app/customers",
        },
        // {
        //   title: "Payment Methods",
        //   url: "#",
        // },
      ],
    },
    {
      title: "Invoices",
      url: "/app/invoices",
      icon: FileText,
      items: [
        {
          title: "Generate Invoice",
          url: "/app/invoices/create",
        },
        {
          title: "Invoice History",
          url: "/app/invoices",
        },
        // {
        //   title: "Pending Payments",
        //   url: "#",
        // },
        {
          title: "Receipts",
          url: "/app/receipts",
        },
      ],
    },
    {
      title: "Staff",
      url: "/app/users",
      icon: Users,
      items: [
        {
          title: "Employees",
          url: "/app/users",
        },
        {
          title: "Roles & Permissions",
          url: "/app/users/roles",
        },
        // {
        //   title: "Attendance & Shifts",
        //   url: "#",
        // },
      ],
    },
    {
      title: "Reports",
      url: "#",
      icon: BarChart2,
      items: [
        {
          title: "Sales Reports",
          url: "/app/reports",
        },
        // {
        //   title: "Inventory Reports",
        //   url: "#",
        // },
        // {
        //   title: "Staff Performance",
        //   url: "#",
        // },
        // {
        //   title: "Profit & Loss",
        //   url: "#",
        // },
      ],
    },
    {
      title: "Integrations",
      url: "#",
      icon: Settings,
      items: [
        {
          title: "M-Pesa",
          url: "/app/integrations/mpesa",
        },
        // {
        //   title: "Stripe Terminal",
        //   url: "#",
        // },
        // {
        //   title: "Integrations",
        //   url: "#",
        // },
      ],
    },
    {
      title: "Settings",
      url: "#",
      icon: Settings,
      items: [
        {
          title: "Store Information",
          url: "/app/settings/stores/information",
        },
        // {
        //   title: "Taxes",
        //   url: "#",
        // },
        // {
        //   title: "Integrations",
        //   url: "#",
        // },
      ],
    },
  ],

  staffNav: [
   {
      title: "Inventory",
      url: "#",
      icon: Package,
      items: [
        {
          title: "Products",
          url: "/app/items",
        },
        // {
        //   title: "Stock Management",
        //   url: "#",
        // },
       
        {
          title: "Exchanges",
          url: "/app/items/exchanges",
        },
        {
          title: "Returns",
          url: "/app/items/returns",
        },
        {
          title: "Categories",
          url: "/app/items/categories",
        },
        {
          title: "Brands",
          url: "/app/items/brands",
        },
        
      ],
    },
    {
      title: "Sales",
      url: "/app/sales",
      icon: Receipt,
      items: [
       
       
        {
          title: "Transactions",
          url: "/app/sales/transactions",
          isActive: true,
        },
        
        {
          title: "Returns & Refunds",
          url: "/app/refunds",
        },
        {
          title: "Customer Management",
          url: "/app/customers",
        },
        
      ],
    },
    
    
  ],

  managerNav: [
    {
      title: "Inventory",
      url: "#",
      icon: Package,
      items: [
        {
          title: "Products",
          url: "/app/items",
        },
        // {
        //   title: "Stock Management",
        //   url: "#",
        // },
       
        {
          title: "Exchanges",
          url: "/app/items/exchanges",
        },
        {
          title: "Returns",
          url: "/app/items/returns",
        },
        {
          title: "Categories",
          url: "/app/items/categories",
        },
        {
          title: "Brands",
          url: "/app/items/brands",
        },
        
      ],
    },
    {
      title: "Sales",
      url: "/app/sales",
      icon: Receipt,
      items: [
       
        {
          title: "Sales History",
          url: "/app/sales",
          isActive: true,
        },
        {
          title: "Transactions",
          url: "/app/sales/transactions",
          isActive: true,
        },
        
        {
          title: "Returns & Refunds",
          url: "/app/refunds",
        },
        {
          title: "Customer Management",
          url: "/app/customers",
        },
        
      ],
    },
    {
      title: "Invoices",
      url: "/app/invoices",
      icon: FileText,
      items: [
        {
          title: "Generate Invoice",
          url: "/app/invoices/create",
        },
        {
          title: "Invoice History",
          url: "/app/invoices",
        },
        // {
        //   title: "Pending Payments",
        //   url: "#",
        // },
        {
          title: "Receipts",
          url: "/app/receipts",
        },
      ],
    },
   
   {
      title: "Staff",
      url: "/app/users",
      icon: Users,
      items: [
        {
          title: "Employees",
          url: "/app/users",
        },
        {
          title: "Roles & Permissions",
          url: "/app/users/roles",
        },
        // {
        //   title: "Attendance & Shifts",
        //   url: "#",
        // },
      ],
    },
    {
      title: "Reports",
      url: "#",
      icon: BarChart2,
      items: [
        {
          title: "Sales Reports",
          url: "/app/reports",
        },
        // {
        //   title: "Inventory Reports",
        //   url: "#",
        // },
        // {
        //   title: "Staff Performance",
        //   url: "#",
        // },
        // {
        //   title: "Profit & Loss",
        //   url: "#",
        // },
      ],
    },
  ],
};



export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader>
        <Image
          src="/logo-out.png"
          alt="StoreYako Logo"
          width={113}
          height={45}
        />
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={data.navMain} />
      </SidebarContent>

      <SidebarRail />
    </Sidebar>
  );
}
