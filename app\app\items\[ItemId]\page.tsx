//app/app/items/[ItemId]/page.tsx
"use client";

import { fetchItem } from '@/lib/api/items/service';
import { Item } from '@/lib/api/items/models';
import { getStore } from '@/lib/api/retailStores/service';
import { Store } from '@/lib/api/retailStores/models';
import { fetchBrands } from '@/lib/api/brands/service';
import { Brand } from '@/lib/api/brands/models';
import { fetchCategories } from '@/lib/api/categories/service';
import { Category } from '@/lib/api/categories/models';
import { fetchSuppliers } from '@/lib/api/suppliers/service';
import { Supplier } from '@/lib/api/suppliers/models';
import { notFound, useRouter } from 'next/navigation';
import Cookies from 'js-cookie';
import { useState, useEffect, useCallback, useMemo } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { PaymentMethod, SellItemSchema, SellItemsSchema } from "@/lib/api/sales/models";
import { sellItems, SellItemsResult } from "@/lib/api/sales/service";
import { Loader2, ShoppingCart, RefreshCw } from "lucide-react"; // Still needed for modal overlay
import { CartItem, CheckoutDialog } from "../../../components/CheckoutDialog";
import { useWebSocket } from "@/lib/hooks/useWebSocket";
import toast from 'react-hot-toast';

// Note: LoadingSpinner component is kept as it's used in the ModalOverlay
function LoadingSpinner() {
  return <Loader2 className="h-8 w-8 animate-spin text-primary" />;
}

function ModalOverlay({ children }: { children: React.ReactNode }) {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-60 flex justify-center items-center z-[100]">
      <div className="bg-background p-4 rounded-lg shadow-xl border">
        {children}
        <p className="text-center text-sm mt-2 text-muted-foreground">Processing...</p>
      </div>
    </div>
  );
}


interface ItemPageProps {
  params: {
    ItemId: string;
  };
}

export default function ItemPage({ params }: ItemPageProps) {
  const router = useRouter();
  const itemId = params.ItemId;
  const [item, setItem] = useState<Item | null>(null);
  const [store, setStore] = useState<Store | null>(null);
  const [brands, setBrands] = useState<Brand[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [suppliers, setSuppliers] = useState<Supplier[]>([]);
  const [initialLoading, setInitialLoading] = useState(true); // Renamed loading state
  const [error, setError] = useState<Error | null>(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [cart, setCart] = useState<CartItem[]>([]);
  const [isCartOpen, setIsCartOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false); // Separate state for other operations
  const [paymentMethod, setPaymentMethod] = useState<PaymentMethod | null>(null);
  const [mpesaPhoneNumber, setMpesaPhoneNumber] = useState("");
  const [itemDiscounts, setItemDiscounts] = useState<{ [itemId: string]: boolean }>({});
  const token = Cookies.get("auth_token");
  const store_id = Cookies.get("active_store");
  const { connectionStatus, lastMessage, reconnect } = useWebSocket(!!token);
  const [pendingReceiptId, setPendingReceiptId] = useState<string | null>(null);

  const refreshItemData = useCallback(() => {
    if (token && store_id && itemId) {
      fetchItem(token, store_id, itemId)
        .then(fetchedItem => {
          setItem(fetchedItem);
          if (fetchedItem) {
            setCart(currentCart => currentCart.map(ci => ci.id === fetchedItem.id ? {
              ...ci,
              price: fetchedItem.default_price,
              has_discount: fetchedItem.has_discount,
              discount: fetchedItem.discount || 0
            } : ci));
          }
        })
        .catch(e => {
          console.error("Failed to refresh item data", e);
          toast.error("Could not refresh item details.");
          if (e.message?.includes('404') || e.status === 404) {
            setError(new Error("Item not found after refresh. It might have been deleted."));
            setItem(null);
          }
        });
    }
  }, [token, store_id, itemId]);

  useEffect(() => {
    const fetchData = async () => {
      if (!token || !store_id) {
        toast.error("Authentication or store information missing.");
        setInitialLoading(false);
        setError(new Error("Missing credentials"));
        return;
      }

      setInitialLoading(true);
      setError(null);
      try {
        const fetchedItem = await fetchItem(token, store_id, itemId);
        setItem(fetchedItem);

        const [fetchedStore, fetchedBrands, fetchedCategories, fetchedSuppliers] = await Promise.all([
          getStore(store_id, token),
          fetchBrands(token, store_id),
          fetchCategories(token, store_id),
          fetchSuppliers(token, store_id)
        ]);

        setStore(fetchedStore);
        setBrands(fetchedBrands);
        setCategories(fetchedCategories);
        setSuppliers(fetchedSuppliers);

      } catch (err: any) {
        console.error("Error fetching initial data:", err);
        setError(err);
        toast.error(`Failed to load item details: ${err.message || 'Unknown error'}`);
        if (err.status === 404) {
          notFound();
        }
      } finally {
        setInitialLoading(false); // Set loading state to false here
      }
    };

    fetchData();
  }, [itemId, token, store_id, router]); // Added router to dependencies as per hook recommendation

  const formatPrice = useCallback((price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: store?.currency || 'USD',
    }).format(price);
  }, [store?.currency]);

  const handleDelete = async () => {
    if (!token || !store_id || !item) return;

    toast((t) => (
      <div className="flex flex-col gap-2 items-center p-1">
        <p className="text-sm font-medium">Delete "{item.name}"?</p>
        <p className="text-xs text-muted-foreground">This action cannot be undone.</p>
        <div className="flex gap-2 mt-2 w-full justify-center">
          <Button
            variant="destructive" size="sm" className="flex-1"
            onClick={async () => {
              toast.dismiss(t.id);
              setShowDeleteModal(false);
              setIsLoading(true); // Use general loading state for actions
              const deletingToastId = toast.loading(`Deleting ${item.name}...`);
              try {
                const response = await fetch(`/api/items/${itemId}`, {
                  method: 'DELETE',
                  headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                  },
                });

                if (!response.ok) {
                  const errorData = await response.json();
                  throw new Error(errorData.message || `Failed to delete item (Status: ${response.status})`);
                }

                toast.success(`"${item.name}" deleted successfully.`, { id: deletingToastId });
                router.push('/app/items');
              } catch (error: any) {
                console.error("Deletion failed:", error);
                toast.error(`Deletion failed: ${error.message || 'Unknown error'}`, { id: deletingToastId });
                setError(error); // Maybe set the general page error?
              } finally {
                setIsLoading(false); // Stop general loading state
              }
            }}
          > Confirm Delete </Button>
          <Button variant="outline" size="sm" className="flex-1" onClick={() => {
            toast.dismiss(t.id);
            setShowDeleteModal(false);
          }}>
            Cancel
          </Button>
        </div>
      </div>
    ), { duration: Infinity });

    setShowDeleteModal(true);
  };

  const handleSellClick = useCallback(() => {
    if (!item || item.quantity === 0) {
      toast.error(item ? `${item.name} is out of stock.` : "Item data not loaded.");
      return;
    }
    setCart([{
      id: item.id,
      name: item.name,
      price: item.default_price,
      quantity: 1,
      image: item.image,
      has_discount: item.has_discount,
      discount: item.discount || 0,
      vat_included: item.vat_included ?? false,
      vat_percentage: item.vat_percentage ?? 0
    }]);
    setItemDiscounts({ [item.id]: false });
    setPaymentMethod(null);
    setMpesaPhoneNumber("");
    setIsCartOpen(true);
  }, [item]);

  const updateCartItemQuantity = useCallback((cartItemId: string, quantity: number) => {
    if (!item) return;
    setCart(prevCart => {
      const newCart = prevCart.map(cartItem =>
        cartItem.id === cartItemId
          ? { ...cartItem, quantity: Math.max(1, Math.min(quantity, item.quantity)) }
          : cartItem
      );
      // If quantity becomes 0 (which shouldn't happen with Math.max(1,...), but as safeguard)
      // or if the cart becomes empty, consider closing the cart.
      if (newCart.length > 0 && newCart[0].quantity === 0) {
        setIsCartOpen(false);
        return [];
      }
      // If cart becomes empty after update (e.g., if removing last item)
      if (newCart.length === 0) {
        setIsCartOpen(false);
      }
      return newCart;
    });
  }, [item]);


  const updateCartItemPrice = useCallback((cartItemId: string, price: number) => {
    setCart(prevCart =>
      prevCart.map(cItem =>
        cItem.id === cartItemId
          ? { ...cItem, price: Math.max(0, price) } // Ensure price isn't negative
          : cItem
      )
    );
  }, []);

  const removeFromCart = useCallback((cartItemId: string) => {
    // Since this page only ever sells ONE item type at a time, removing means emptying the cart.
    setCart([]);
    setItemDiscounts({});
    setIsCartOpen(false);
    toast.success("Sale cancelled.");
  }, []);

  const toggleItemDiscount = useCallback((cartItemId: string) => {
    setItemDiscounts(prev => ({ ...prev, [cartItemId]: !prev[cartItemId] }));
  }, []);

  const getItemTotal = useCallback((cartItem: CartItem) => {
    const baseTotal = cartItem.price * cartItem.quantity;
    // Apply discount only if toggled ON for this item in the cart AND item has discount enabled AND discount > 0
    if (itemDiscounts[cartItem.id] && cartItem.has_discount && cartItem.discount > 0) {
      return baseTotal * (1 - cartItem.discount / 100);
    }
    return baseTotal;
  }, [itemDiscounts]); // Dependency on itemDiscounts state

  const cartTotal = useMemo(() => cart.reduce((total, cartItem) => total + getItemTotal(cartItem), 0), [cart, getItemTotal]);


  const performCheckout = useCallback(async (itemsToSell: SellItemSchema[], totalAmount: number): Promise<SellItemsResult> => {
    let errorMsg: string | null = null;
    if (!token || !store_id) errorMsg = "Authentication credentials missing.";
    else if (itemsToSell.length === 0) errorMsg = "Your cart is empty.";
    else if (!paymentMethod) errorMsg = "Please select a payment method.";
    else {
      // Basic M-Pesa validation (adjust regex as needed for stricter/different formats)
            const mpesaRegex = /^(?:0|(?:254|\+254))(7|1)[0-9]{8}$/;

      if (paymentMethod === "mpesa" && (!mpesaPhoneNumber || !mpesaRegex.test(mpesaPhoneNumber))) {
        errorMsg = "Please enter a valid M-Pesa number (e.g., 2547...).";
      }
    }
    if (errorMsg) { toast.error(errorMsg); return { ok: false, status: 400, error: errorMsg } as SellItemsResult; }


    const sellItemsPayload: SellItemsSchema = {
      items: itemsToSell,
      payment_method: paymentMethod!,
      mpesa_payload: paymentMethod === "mpesa" ? { amount: totalAmount, phone_number: mpesaPhoneNumber } : null
    };

    setIsLoading(true); // Use general loading state
    const loadingToastId = toast.loading('Processing sale...');
    try {
      const result = await sellItems(token!, sellItemsPayload); // Pass payload directly
      toast.dismiss(loadingToastId);
      return result;
    } catch (error: any) {
      console.error("Checkout API call failed:", error);
      toast.error(`Checkout failed: ${error.message || 'Network error'}`, { id: loadingToastId });
      return { ok: false, status: 500, error: error.message || "API Call Error" } as SellItemsResult;
    } finally {
      // Only stop general loading if NOT waiting for M-Pesa callback
      if (paymentMethod !== 'mpesa') {
        setIsLoading(false); // Stop general loading state
      }
    }
  }, [token, store_id, paymentMethod, mpesaPhoneNumber]);


  const handleCheckout = async () => {
    if (!store_id) { toast.error("Active store is not set."); return; }
    if (cart.length === 0 || !item) { toast.error("Cart is empty or item data missing."); return; }

    // Prepare items for the API call, considering discounts
    const itemsToSell: SellItemSchema[] = cart.map(cartItem => {
      const useDiscount = itemDiscounts[cartItem.id] && cartItem.has_discount && cartItem.discount > 0;
      const discountedUnitPrice = useDiscount ? cartItem.price * (1 - cartItem.discount / 100) : cartItem.price;
      return { item_uuid: cartItem.id, price: discountedUnitPrice, quantity: cartItem.quantity, store_id: store_id };
    });

    setPendingReceiptId(null); // Clear any previous pending ID
    const result = await performCheckout(itemsToSell, cartTotal);

    if (result.ok) {
      if (result.status === 200 && result.data?.receipt_number) { // Successful immediate sale
        toast.success(`Sale successful! Receipt: ${result.data.receipt_number}`);
        // Reset state after successful sale
        setCart([]); setPaymentMethod(null); setMpesaPhoneNumber(""); setItemDiscounts({}); setIsCartOpen(false);
        refreshItemData(); // Refresh item quantity
        setIsLoading(false); // Explicitly stop loading as it's done
      } else if (result.status === 202 && result.data?.receiptId) { // M-Pesa initiated
        setPendingReceiptId(result.data.receiptId);
        // Show loading toast for M-Pesa, use receiptId as ID for later dismissal
        toast.loading(`M-Pesa request sent... (Ref: ${result.data.receiptId.substring(0, 8)})`, { id: result.data.receiptId, duration: 90000 }); // 90 seconds timeout
        setIsCartOpen(false); // Close cart while waiting
        // isLoading remains true implicitly via pendingReceiptId check or explicitly if needed
      } else {
        // Unexpected success response
        toast.error('Sale submitted, but received an unexpected success response.');
        setIsLoading(false); // Stop loading on unexpected result
      }
    } else {
      // API call failed, error toast shown in performCheckout
      setIsLoading(false); // Stop loading on failure
    }
  };


  useEffect(() => {
    if (lastMessage && pendingReceiptId && typeof lastMessage === 'object' && lastMessage.type === 'payment_status' && lastMessage.receiptId === pendingReceiptId) {
      const currentPendingId = pendingReceiptId;
      setPendingReceiptId(null); // Clear pending state
      setIsLoading(false); // Stop loading indicator
      toast.dismiss(currentPendingId); // Dismiss the specific M-Pesa loading toast

      const { status, message, mpesaReceiptNumber } = lastMessage;
      if (status === 'completed') {
        toast.success(`M-Pesa Payment Completed! ${mpesaReceiptNumber ? `Receipt: ${mpesaReceiptNumber}` : ''}`, { duration: 8000 });
        // Reset cart state (already closed, but ensure consistency)
        setCart([]); setPaymentMethod(null); setMpesaPhoneNumber(""); setItemDiscounts({});
        // Refresh data after a short delay to allow backend processing
        setTimeout(refreshItemData, 250);
      } else { // failed or cancelled
        toast.error(`M-Pesa Payment Failed: ${message || 'Transaction unsuccessful.'}`, { duration: 10000 });
        // Optionally, reset payment method so user can choose again, but keep cart open?
        // Or just show error and let them retry? Current: Reset payment method.
        setPaymentMethod(null);
        // Do NOT close cart here, allow user to retry or change method
      }
    }
  }, [lastMessage, pendingReceiptId, refreshItemData]); // Added refreshItemData dependency


  // --- UPDATED LOADING UI ---
  if (initialLoading) {
    return (
      <div className="min-h-screen flex justify-center items-center bg-gray-50">
        <div className="animate-pulse flex flex-col items-center">
          {/* Spinner */}
          <div className="h-12 w-12 rounded-full border-4 border-t-blue-500 border-gray-200 animate-spin"></div>
          {/* Loading Text */}
          <p className="mt-4 text-gray-600 font-medium">Loading item details...</p>
        </div>
      </div>
    );
  }
  // --- END UPDATED LOADING UI ---

  if (error && !item) { // Error state - show specific error message
    return (
      <div className="min-h-screen flex justify-center items-center bg-gray-50">
        <div className="bg-white p-8 rounded-lg shadow-md max-w-md w-full">
          <div className="text-red-500 flex items-center justify-center mb-4">
            {/* Simple Error Icon */}
            <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h2 className="text-xl font-bold text-center mb-2 text-red-600">Error Loading Item</h2>
          <p className="text-gray-600 text-center mb-4">{error.message || 'Could not load item details.'}</p>
          <div className="mt-6 flex justify-center">
            <Link href="/app/items">
              <Button variant="outline">Return to Items</Button>
            </Link>
          </div>
        </div>
      </div>
    );
  }

  if (!item) { // Item not found state (after loading finishes)
    return (
      <div className="min-h-screen flex justify-center items-center bg-gray-50">
        <div className="bg-white p-8 rounded-lg shadow-md max-w-md w-full">
          <div className="text-amber-500 flex items-center justify-center mb-4">
            {/* Simple Not Found Icon */}
            <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h2 className="text-xl font-bold text-center mb-2">Item Not Found</h2>
          <p className="text-gray-600 text-center mb-4">The requested item could not be found or may have been deleted.</p>
          <div className="mt-6 flex justify-center">
            <Link href="/app/items">
              <Button variant="outline">Return to Items</Button>
            </Link>
          </div>
        </div>
      </div>
    );
  }

  // Find associated names
  const brandName = brands.find((b) => b.id === item.brand)?.name || '—';
  const categoryName = categories.find((c) => c.id === item.category)?.name || '—';
  const supplierName = suppliers.find((s) => s.id === item.vendor_id)?.name || '—';

  // --- Component Render (Main Content) ---
  return (
    <div className="bg-gray-50 min-h-screen pb-20">
      {/* Modal Overlay for general loading actions (like delete, checkout) */}
      {isLoading && <ModalOverlay><LoadingSpinner /></ModalOverlay>}

      {/* Sticky Header/Breadcrumb */}
      <div className="bg-white shadow-sm sticky top-0 z-10">
        <div className="container mx-auto px-4 py-3">
          <div className="flex text-sm text-gray-500">
            <Link href="/app/dashboard" className="hover:text-blue-600">Dashboard</Link>
            <span className="mx-2">/</span>
            <Link href="/app/items" className="hover:text-blue-600">Items</Link>
            <span className="mx-2">/</span>
            <span className="text-gray-700 font-medium truncate">{item.name}</span>
          </div>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="container mx-auto px-4 pt-6">
        {/* Item Header & Sell Button */}
        <div className="mb-6 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-800">{item.name}</h1>
            <p className="text-gray-500 text-sm">SKU: {item.sku} • Added on {new Date(item.date_created).toLocaleDateString()}</p>
          </div>
          <Button
            variant="default"
            onClick={handleSellClick}
            className="px-5 py-2 w-full sm:w-auto"
            // Disable if general loading, initial loading, out of stock, or waiting for M-Pesa
            disabled={isLoading || initialLoading || item.quantity === 0 || pendingReceiptId !== null}
          >
            <ShoppingCart className="mr-2 h-4 w-4" />
            {item.quantity === 0 ? 'Out of Stock' : 'Sell This Item'}
          </Button>
        </div>

        {/* Item Details Card */}
        <div className="bg-white rounded-xl shadow-sm overflow-hidden">
          <div className="grid grid-cols-1 md:grid-cols-3">
            {/* Image Column */}
            <div className="md:col-span-1 p-6 flex items-center justify-center bg-gray-50 border-b md:border-b-0 md:border-r border-gray-100 max-h-[400px] md:max-h-none">
              {item.image ? (
                <img
                  src={item.image}
                  alt={item.name}
                  className="w-auto h-auto max-w-full max-h-full object-contain rounded-lg"
                  // Add error handler for broken images
                  onError={(e) => (e.currentTarget.src = '/placeholder-image.svg')} // Provide a fallback SVG or path
                />
              ) : (
                <div className="w-full aspect-square bg-gray-100 flex flex-col items-center justify-center rounded-lg text-gray-400 p-4">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                  <span className="text-center text-sm">No Image Available</span>
                </div>
              )}
            </div>

            {/* Details Columns */}
            <div className="md:col-span-2 p-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-x-12 gap-y-6">
                {/* Product Information */}
                <div>
                  <h2 className="text-lg font-semibold mb-3 text-gray-700 border-b pb-2">Product Information</h2>
                  <dl className="space-y-3">
                    <div className="grid grid-cols-3 gap-2">
                      <dt className="text-sm text-gray-500 font-medium col-span-1">Brand</dt>
                      <dd className="text-gray-800 text-sm col-span-2">{brandName}</dd>
                    </div>
                    <div className="grid grid-cols-3 gap-2">
                      <dt className="text-sm text-gray-500 font-medium col-span-1">Category</dt>
                      <dd className="text-gray-800 text-sm col-span-2">{categoryName}</dd>
                    </div>
                    <div className="grid grid-cols-3 gap-2">
                      <dt className="text-sm text-gray-500 font-medium col-span-1">Supplier</dt>
                      <dd className="text-gray-800 text-sm col-span-2">{supplierName}</dd>
                    </div>
                    {item.description && (
                      <div className="grid grid-cols-3 gap-2">
                        <dt className="text-sm text-gray-500 font-medium col-span-1">Description</dt>
                        <dd className="text-gray-800 text-sm col-span-2 whitespace-pre-wrap">{item.description}</dd>
                      </div>
                    )}
                    {item.notes && (
                      <div className="grid grid-cols-3 gap-2">
                        <dt className="text-sm text-gray-500 font-medium col-span-1">Notes</dt>
                        <dd className="text-gray-800 text-sm col-span-2 whitespace-pre-wrap">{item.notes}</dd>
                      </div>
                    )}
                  </dl>
                </div>

                {/* Inventory & Pricing */}
                <div>
                  <h2 className="text-lg font-semibold mb-3 text-gray-700 border-b pb-2">Inventory & Pricing</h2>
                  <dl className="space-y-3">
                    <div className="grid grid-cols-3 gap-2 items-center bg-blue-50 p-2 rounded-md">
                      <dt className="text-sm text-blue-700 font-medium col-span-1">Price</dt>
                      <dd className="text-lg font-bold text-blue-700 text-sm col-span-2">{formatPrice(item.default_price)}</dd>
                    </div>
                    <div className="grid grid-cols-3 gap-2 items-center">
                      <dt className="text-sm text-gray-500 font-medium col-span-1">Cost</dt>
                      <dd className="text-gray-800 font-semibold text-sm col-span-2">{formatPrice(item.default_cost ?? 0)}</dd>
                    </div>
                    {item.has_discount && (
                      <div className="grid grid-cols-3 gap-2 items-center bg-green-50 p-2 rounded-md">
                        <dt className="text-sm text-green-700 font-medium col-span-1">Discount</dt>
                        <dd className="text-green-700 font-semibold text-sm col-span-2">{item.discount}%</dd>
                      </div>
                    )}
                    <div className="grid grid-cols-3 gap-2 items-center">
                      <dt className="text-sm text-gray-500 font-medium col-span-1">Quantity</dt>
                      <dd className={`text-sm col-span-2 flex items-center font-medium ${item.quantity > 10 ? 'text-green-600' :
                        item.quantity > 0 ? 'text-yellow-600' : 'text-red-600'
                        }`}>
                        <span className={`inline-block h-2.5 w-2.5 rounded-full mr-2 ${item.quantity > 10 ? 'bg-green-500' :
                          item.quantity > 0 ? 'bg-yellow-500' : 'bg-red-500'
                          }`}></span>
                        {item.quantity} {item.quantity <= 0 ? ' (Out of Stock)' : item.quantity <= 10 ? ' (Low Stock)' : ''}
                      </dd>
                    </div>
                    <div className="grid grid-cols-3 gap-2">
                      <dt className="text-sm text-gray-500 font-medium col-span-1">SKU</dt>
                      <dd className="text-gray-800 text-sm col-span-2">{item.sku}</dd>
                    </div>
                    <div className="grid grid-cols-3 gap-2">
                      <dt className="text-sm text-gray-500 font-medium col-span-1">Barcode</dt>
                      <dd className="text-gray-800 text-sm col-span-2">{item.barcode || '—'}</dd>
                    </div>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          {/* Action Buttons Footer */}
          <div className="p-4 border-t border-gray-200 flex justify-end gap-3">
            <Button variant="outline" className="px-4 py-2" disabled={isLoading || initialLoading || pendingReceiptId !== null} onClick={() => { router.push(`/app/items/${itemId}/edit`); }}>
              Edit
            </Button>
            <Button
              variant="destructive"
              onClick={handleDelete}
              className="px-4 py-2"
              disabled={isLoading || initialLoading || pendingReceiptId !== null} // Disable during any loading state
            >
              Delete
            </Button>
          </div>
        </div>
      </div>

      {/* Delete Confirmation Dialog (No UI changes needed here) */}
      {showDeleteModal && (
        <Dialog open={showDeleteModal} onOpenChange={setShowDeleteModal}>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Delete Item</DialogTitle>
              <DialogDescription>
                Are you sure you want to delete "{item.name}"? This action cannot be undone.
                Click "Confirm Delete" in the notification pop-up to proceed.
              </DialogDescription>
            </DialogHeader>
            <div className="mt-4 flex justify-end gap-2">
              <Button variant="secondary" onClick={() => setShowDeleteModal(false)}>Close</Button>
            </div>
          </DialogContent>
        </Dialog>
      )}

      {/* Checkout Dialog */}
      <CheckoutDialog
        isOpen={isCartOpen}
        onOpenChange={setIsCartOpen}
        cart={cart}
        onUpdateQuantity={updateCartItemQuantity}
        onUpdatePrice={updateCartItemPrice}
        onRemoveItem={removeFromCart}
        onCheckout={handleCheckout}
        // Pass correct loading state: general loading OR waiting for M-Pesa
        isLoading={isLoading || pendingReceiptId !== null}
        storeCurrency={store?.currency}
        paymentMethod={paymentMethod}
        mpesaPhoneNumber={mpesaPhoneNumber}
        onPaymentMethodChange={setPaymentMethod}
        onMpesaPhoneNumberChange={setMpesaPhoneNumber}
        itemDiscounts={itemDiscounts}
        onToggleItemDiscount={toggleItemDiscount}
        cartTotal={cartTotal}
      />

      {/* WebSocket Status Indicator */}
      <div className="fixed bottom-4 right-4 flex items-center gap-2 p-2 bg-background border rounded-full shadow-md text-xs z-40">
        <div title={`Connection: ${connectionStatus}`} className={`w-3 h-3 rounded-full ${connectionStatus === 'open' ? 'bg-green-500 animate-pulse' :
          connectionStatus === 'connecting' ? 'bg-yellow-500 animate-pulse' : 'bg-red-500'
          }`} />
        <span className="hidden sm:inline">
          {connectionStatus === 'open' ? 'Live' : connectionStatus === 'connecting' ? 'Connecting...' : 'Offline'}
        </span>
        {(connectionStatus === 'closed' || connectionStatus === 'error') && (
          <Button variant="ghost" size="sm" onClick={reconnect} className="h-6 px-1 text-xs text-primary hover:bg-muted" title="Attempt Reconnect">
            <RefreshCw className="h-3 w-3" />
          </Button>
        )}
        {pendingReceiptId && (
          <span className="hidden sm:inline text-blue-600 animate-pulse ml-1" title="Waiting for M-Pesa confirmation">(Waiting...)</span>
        )}
      </div>
    </div>
  );
}