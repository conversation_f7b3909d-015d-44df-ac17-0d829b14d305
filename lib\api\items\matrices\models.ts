export interface ItemMatrixSchema {
  store_id: string;
  name: string;
  description: string;
  matrix_type: string;
  category: string;
  brand: string;
  vendor_id: string;
  default_price: number;
  default_currency?: string;
  msrp?: number;
  default_cost: number;
  discount?: number;
  has_discount?: boolean;
  vat_included?: boolean;
  vat_percentage?: number;
}

export interface ItemMatrix {
  id: string;
  store_id: string;
  description: string;
  name: string;
  matrix_type: string;
  category: string;
  brand: string;
  vendor_id: string;
  default_price: number;
  date_created: string;
  default_currency: string;
  date_updated?: string;
  msrp?: number;
  default_cost: number;
  discount?: number;
  has_discount: boolean;
  vat_included: boolean;
  vat_percentage: number;
}
