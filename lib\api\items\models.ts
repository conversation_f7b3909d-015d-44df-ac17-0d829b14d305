export interface Item {
  id: string;
  store_id: string;
  name: string;
  image?: string;
  quantity: number;
  brand: string;
  category: string;
  default_cost?: number;
  default_price: number;
  has_discount: boolean;
  discount?: number | null;
  date_created: string;
  date_updated?: string | null;
  tags: string[];
  vendor_id?: string | null;
  is_variant: boolean;
  parent_item_id?: string | null;
  sku?: string | null;
  description?: string | null;
  notes?: string | null;
  barcode?: string | null;
  vat_included: boolean;
  vat_percentage: number;
}

export interface ItemSchema {
  name: string;
  quantity: number;
  default_cost?: number;
  default_price: number;
  brand: string;
  has_discount: boolean;
  date_created: string;
  tags: string[];
  category: string;
  store_id: string;
  vendor_id?: string | null;
  image?: File | string;
  description?: string | null;
  sku?: string;
  barcode?: string;
  discount?: number | null;
  notes?: string | null;
  is_variant: boolean;
  parent_item_id?: string | null;
  vat_included: boolean;
  vat_percentage: number;
}