import { ref, uploadBytes, getDownloadURL, deleteObject } from "firebase/storage";
import { storage } from "./config";
import { BASE_URL } from "@/app/configs/constants";

interface BackendUploadResponse {
  location: string; // This 'location' field contains the signed URL
}

/**
 * Makes an API call to your backend to request a signed URL for file upload.
 * @param storeId The ID of the store.
 * @param folder The folder path (e.g., 'products', 'store_logos').
 * @param filename The final filename for the object in GCS.
 * @param contentType The MIME type of the file (e.g., 'image/png').
 * @returns Promise<BackendUploadResponse> containing the signed URL (in 'location' field).
 */
async function requestSignedUploadUrlFromBackend(
  storeId: string,
  folder: string,
  filename: string,
  authToken: string,
  contentType: string
): Promise<BackendUploadResponse> {
  // Construct the backend API URL based on your specified route structure
  const backendApiUrl = `${BASE_URL}/signed_upload/${storeId}/${folder}/${filename}`;

  console.log(`Requesting signed URL from backend: ${backendApiUrl} for type: ${contentType}`);

  try {
    const response = await fetch(backendApiUrl, {
      method: 'GET', // Assuming your backend uses GET for signed URL generation
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`,
      },
      // If your backend needs contentType as a query parameter:
      // body: JSON.stringify({ contentType }), // Example if you need to send body with GET
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`Failed to get signed URL from backend: ${errorData.message || response.statusText}`);
    }

    const data: BackendUploadResponse = await response.json();
    console.log('Received signed URL response:', data);
    return data;
  } catch (error) {
    console.error('Error fetching signed URL from backend:', error);
    throw new Error(`Failed to connect to backend for signed URL: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}


// --- New Upload Function using Signed URLs ---

/**
 * Uploads a file directly to Google Cloud Storage using a pre-signed URL.
 * This function does NOT use Firebase Storage SDK for the upload itself.
 * @param file The file object to upload.
 * @param signedUrl The pre-signed URL obtained from your backend.
 * @returns Promise<void>
 */
async function uploadFileDirectlyToGCS(file: File, signedUrl: string): Promise<void> {
  try {
    console.log('=== Starting GCS Upload ===');
    console.log('File details:', {
      fileName: file.name,
      fileType: file.type,
      fileSize: file.size,
    });
    console.log('Signed URL (first 150 chars):', signedUrl.substring(0, 150) + '...');

    console.log('Proceeding with PUT request...');
    const response = await fetch(signedUrl, {
      method: 'PUT',
      headers: {
        'Content-Type': file.type || 'application/octet-stream',
      },
      body: file,
    });

    console.log('=== Fetch completed ===');
    console.log('Response status:', response.status);
    console.log('Response statusText:', response.statusText);
    console.log('Response ok:', response.ok);
    console.log('Response headers:', Object.fromEntries(response.headers.entries()));

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Error response from GCS:', errorText);
      throw new Error(`Upload failed: ${response.status} ${response.statusText} - ${errorText}`);
    }

    const responseText = await response.text();
    console.log('Response body:', responseText);
    console.log('=== Upload successful ===');
  } catch (error) {
    console.error('=== Upload failed ===');
    console.error('Error details:', error);
    if (error instanceof Error) {
      console.error('Error name:', error.name);
      console.error('Error message:', error.message);
      console.error('Error stack:', error.stack);
    }

    // Check if it's a network error
    if (error instanceof TypeError && error.message.includes('fetch')) {
      console.error('This appears to be a network/CORS error');
      console.error('Possible causes:');
      console.error('1. CORS policy blocking the request');
      console.error('2. Invalid signed URL');
      console.error('3. Network connectivity issue');
      console.error('4. Signed URL has expired');
    }

    throw new Error(`Failed to upload file using signed URL: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}


// --- Refactored Orchestration Functions ---

/**
 * Upload a single file to Firebase Storage via a signed URL.
 * This function orchestrates the request for a signed URL from the backend
 * and then performs the direct upload to GCS.
 * @param file - The file to upload
 * @param folder - The folder path (e.g., 'products', 'store_logos')
 * @param storeId - The store ID for organizing files
 * @param authToken - The authentication token for backend authorization
 * @param fileName - Optional custom filename, if not provided, will generate one
 * @returns Promise<string> - The public download URL of the uploaded file
 */
export async function uploadFileWithSignedUrl(
  file: File,
  folder: string,
  storeId: string,
  authToken: string,
  fileName?: string
): Promise<string> {
  try {
    // 1. Generate filename and storage path
    const timestamp = Date.now();
    const fileExtension = file.name.split('.').pop();
    // Changed 'const' to 'let' for finalFileName to allow reassignment
    let finalFileName = fileName || `${timestamp}_${Math.random().toString(36).substring(2)}.${fileExtension}`;

    // Handle special naming for store logos
    if (folder === 'store_logos') {
      const logoFileName = `${storeId}.${fileExtension}`;
      // Ensure finalFileName is updated for store_logos case to match the actual filename used
      // in the backend route.
      finalFileName = logoFileName; // Reassignment is now allowed
    }

    // 2. Request signed URL from backend using individual path components
    const backendResponse = await requestSignedUploadUrlFromBackend(
      storeId,
      folder,
      finalFileName, // Pass the generated filename
      authToken,
      file.type
    );

    const signedUrl = backendResponse.location; // Extract the signed URL from the 'location' field

    // Derive the public URL from the signed URL by removing query parameters
    const publicUrl = signedUrl.split('?')[0];

    // 3. Upload file using the signed URL
    await uploadFileDirectlyToGCS(file, signedUrl);

    console.log('File upload process completed successfully:', publicUrl);
    return publicUrl;
  } catch (error) {
    console.error('Error orchestrating file upload:', error);
    throw new Error(`Failed to upload file: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Upload multiple files to Firebase Storage via signed URLs.
 * @param files - Array of files to upload
 * @param folder - The folder path
 * @param storeId - The store ID for organizing files
 * @param authToken - The authentication token for backend authorization
 * @returns Promise<string[]> - Array of public download URLs
 */
export async function uploadMultipleFiles(
  files: File[],
  folder: string,
  storeId: string,
  authToken: string
): Promise<string[]> {
  try {
    const uploadPromises = files.map(file => uploadFileWithSignedUrl(file, folder, storeId, authToken));
    return await Promise.all(uploadPromises);
  } catch (error) {
    console.error('Error uploading multiple files:', error);
    throw error;
  }
}


// --- Existing Delete and Validation Functions (Unchanged) ---

/**
 * Delete a file from Firebase Storage
 * @param fileUrl - The download URL of the file to delete
 * @param folder - The folder path where the file is stored (can be inferred from URL, but kept for consistency)
 * @param storeId - The store ID for organizing files (can be inferred from URL, but kept for consistency)
 */
export async function deleteFileFromFirebase(fileUrl: string, folder: string, storeId: string): Promise<void> {
  try {
    // Extract the path from the URL
    const url = new URL(fileUrl);
    const pathMatch = url.pathname.match(/\/o\/(.+)$/);
    if (!pathMatch) {
      throw new Error('Invalid Firebase Storage URL');
    }

    // Decode the path
    const fullPath = decodeURIComponent(pathMatch[1]);

    // Create storage reference using the full path
    const storageRef = ref(storage, fullPath);

    // Delete file
    await deleteObject(storageRef);
    console.log('File deleted successfully from Firebase');
  } catch (error) {
    console.error('Error deleting file from Firebase:', error);
    throw error;
  }
}

/**
 * Get file size limit for uploads (10MB default)
 */
export const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB

/**
 * Validate file before upload
 * @param file - File to validate
 * @param allowedTypes - Array of allowed MIME types
 * @param maxSize - Maximum file size in bytes
 */
export function validateFile(
  file: File,
  allowedTypes: string[] = ['image/jpeg', 'image/png', 'image/webp', 'image/gif'],
  maxSize: number = MAX_FILE_SIZE
): { isValid: boolean; error?: string } {
  // Check file type
  if (!allowedTypes.includes(file.type)) {
    return {
      isValid: false,
      error: `File type not allowed. Allowed types: ${allowedTypes.join(', ')}`
    };
  }

  // Check file size
  if (file.size > maxSize) {
    return {
      isValid: false,
      error: `File size too large. Maximum size: ${(maxSize / 1024 / 1024).toFixed(1)}MB`
    };
  }

  return { isValid: true };
}
