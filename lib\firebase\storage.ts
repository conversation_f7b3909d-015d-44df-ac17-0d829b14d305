import { ref, uploadBytes, getDownloadURL, deleteObject } from "firebase/storage";
import { storage } from "./config";
import { BASE_URL } from "@/app/configs/constants";

// /**
//  * Upload a file to Firebase Storage
//  * @param file - The file to upload
//  * @param folder - The folder path (e.g., 'products', 'store_logos')
//  * @param storeId - The store ID for organizing files
//  * @param fileName - Optional custom filename, if not provided, will generate one
//  * @returns Promise<string> - The download URL of the uploaded file
//  */
// export async function uploadFileToFirebase(
//   file: File,
//   folder: string,
//   storeId: string,
//   fileName?: string
// ): Promise<string> {
//   try {
//     // Generate filename if not provided
//     const timestamp = Date.now();
//     const fileExtension = file.name.split('.').pop();
//     const finalFileName = fileName || `${timestamp}_${Math.random().toString(36).substring(2)}.${fileExtension}`;

//     // Create storage reference based on folder type
//     let storagePath: string;
//     if (folder === 'products') {
//       // Products: products/<storeId>/<filename>
//       storagePath = `${folder}/${storeId}/${finalFileName}`;
//     } else if (folder === 'store_logos') {
//       // Store logos: store_logos/<storeId>.png (or other extension)
//       const logoFileName = `${storeId}.${fileExtension}`;
//       storagePath = `${folder}/${logoFileName}`;
//     } else {
//       // Fallback for other folders
//       storagePath = `${folder}/${storeId}/${finalFileName}`;
//     }

//     const storageRef = ref(storage, storagePath);

//     // Upload file
//     const snapshot = await uploadBytes(storageRef, file);

//     // Get download URL
//     const downloadURL = await getDownloadURL(snapshot.ref);

//     console.log('File uploaded successfully to Firebase:', downloadURL);
//     return downloadURL;
//   } catch (error) {
//     console.error('Error uploading file to Firebase:', error);
//     throw new Error(`Failed to upload file: ${error instanceof Error ? error.message : 'Unknown error'}`);
//   }
// }

// /**
//  * Upload multiple files to Firebase Storage
//  * @param files - Array of files to upload
//  * @param folder - The folder path
//  * @param storeId - The store ID for organizing files
//  * @returns Promise<string[]> - Array of download URLs
//  */
// export async function uploadFilesToFirebase(
//   files: File[],
//   folder: string,
//   storeId: string
// ): Promise<string[]> {
//   try {
//     const uploadPromises = files.map(file => uploadFileToFirebase(file, folder, storeId));
//     return await Promise.all(uploadPromises);
//   } catch (error) {
//     console.error('Error uploading multiple files to Firebase:', error);
//     throw error;
//   }
// }

// /**
//  * Delete a file from Firebase Storage
//  * @param fileUrl - The download URL of the file to delete
//  * @param folder - The folder path where the file is stored
//  * @param storeId - The store ID for organizing files
//  */
// export async function deleteFileFromFirebase(fileUrl: string, folder: string, storeId: string): Promise<void> {
//   try {
//     // Extract the path from the URL
//     const url = new URL(fileUrl);
//     const pathMatch = url.pathname.match(/\/o\/(.+)$/);
//     if (!pathMatch) {
//       throw new Error('Invalid Firebase Storage URL');
//     }

//     // Decode the path
//     const fullPath = decodeURIComponent(pathMatch[1]);

//     // Create storage reference using the full path
//     const storageRef = ref(storage, fullPath);

//     // Delete file
//     await deleteObject(storageRef);
//     console.log('File deleted successfully from Firebase');
//   } catch (error) {
//     console.error('Error deleting file from Firebase:', error);
//     throw error;
//   }
// }

// /**
//  * Get file size limit for uploads (10MB default)
//  */
// export const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB

// /**
//  * Validate file before upload
//  * @param file - File to validate
//  * @param allowedTypes - Array of allowed MIME types
//  * @param maxSize - Maximum file size in bytes
//  */
// export function validateFile(
//   file: File,
//   allowedTypes: string[] = ['image/jpeg', 'image/png', 'image/webp', 'image/gif'],
//   maxSize: number = MAX_FILE_SIZE
// ): { isValid: boolean; error?: string } {
//   // Check file type
//   if (!allowedTypes.includes(file.type)) {
//     return {
//       isValid: false,
//       error: `File type not allowed. Allowed types: ${allowedTypes.join(', ')}`
//     };
//   }
  
//   // Check file size
//   if (file.size > maxSize) {
//     return {
//       isValid: false,
//       error: `File size too large. Maximum size: ${(maxSize / 1024 / 1024).toFixed(1)}MB`
//     };
//   }
  
//   return { isValid: true };
// }


interface BackendUploadResponse {
  location: string; // This 'location' field contains the signed URL
}

/**
 * Makes an API call to your backend to request a signed URL for file upload.
 * @param storeId The ID of the store.
 * @param folder The folder path (e.g., 'products', 'store_logos').
 * @param filename The final filename for the object in GCS.
 * @param contentType The MIME type of the file (e.g., 'image/png').
 * @returns Promise<BackendUploadResponse> containing the signed URL (in 'location' field).
 */
async function requestSignedUploadUrlFromBackend(
  storeId: string,
  folder: string,
  filename: string,
  contentType: string
): Promise<BackendUploadResponse> {
  // Construct the backend API URL based on your specified route structure
  const backendApiUrl = `{BASE_URL}/signed_upload/${storeId}/${folder}/${filename}`;

  console.log(`Requesting signed URL from backend: ${backendApiUrl} for type: ${contentType}`);

  try {
    const response = await fetch(backendApiUrl, {
      method: 'GET', // Assuming your backend uses GET for signed URL generation
      headers: {
        'Content-Type': 'application/json', // Or 'text/plain' if your backend doesn't expect JSON
        // You might need to add authorization headers here if your backend requires them
      },
      // If your backend needs contentType as a query parameter:
      // body: JSON.stringify({ contentType }), // Example if you need to send body with GET
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`Failed to get signed URL from backend: ${errorData.message || response.statusText}`);
    }

    const data: BackendUploadResponse = await response.json();
    console.log('Received signed URL response:', data);
    return data;
  } catch (error) {
    console.error('Error fetching signed URL from backend:', error);
    throw new Error(`Failed to connect to backend for signed URL: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}


// --- New Upload Function using Signed URLs ---

/**
 * Uploads a file directly to Google Cloud Storage using a pre-signed URL.
 * This function does NOT use Firebase Storage SDK for the upload itself.
 * @param file The file object to upload.
 * @param signedUrl The pre-signed URL obtained from your backend.
 * @returns Promise<void>
 */
async function uploadFileToSignedUrl(file: File, signedUrl: string): Promise<void> {
  try {
    const response = await fetch(signedUrl, {
      method: 'PUT', // Use PUT for uploading
      headers: {
        'Content-Type': file.type, // Crucial: Set the correct MIME type
      },
      body: file, // The File object itself is sent as the request body
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Error response from GCS:', errorText);
      throw new Error(`Upload failed: ${response.status} ${response.statusText} - ${errorText}`);
    }

    console.log('File uploaded successfully using signed URL.');
  } catch (error) {
    console.error('Error during fetch upload with signed URL:', error);
    throw new Error(`Failed to upload file using signed URL: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}


// --- Refactored Orchestration Functions ---

/**
 * Upload a single file to Firebase Storage via a signed URL.
 * This function orchestrates the request for a signed URL from the backend
 * and then performs the direct upload to GCS.
 * @param file - The file to upload
 * @param folder - The folder path (e.g., 'products', 'store_logos')
 * @param storeId - The store ID for organizing files
 * @param fileName - Optional custom filename, if not provided, will generate one
 * @returns Promise<string> - The public download URL of the uploaded file
 */
export async function uploadFile(
  file: File,
  folder: string,
  storeId: string,
  fileName?: string
): Promise<string> {
  try {
    // 1. Generate filename and storage path
    const timestamp = Date.now();
    const fileExtension = file.name.split('.').pop();
    // Changed 'const' to 'let' for finalFileName to allow reassignment
    let finalFileName = fileName || `${timestamp}_${Math.random().toString(36).substring(2)}.${fileExtension}`;

    // Note: storagePath is no longer directly used for backend URL,
    // but still useful for conceptual understanding or if needed elsewhere.
    let storagePath: string;
    if (folder === 'products') {
      storagePath = `${folder}/${storeId}/${finalFileName}`;
    } else if (folder === 'store_logos') {
      const logoFileName = `${storeId}.${fileExtension}`;
      storagePath = `${folder}/${logoFileName}`;
      // Ensure finalFileName is updated for store_logos case to match the actual filename used
      // in the backend route.
      finalFileName = logoFileName; // Reassignment is now allowed
    } else {
      storagePath = `${folder}/${storeId}/${finalFileName}`;
    }

    // 2. Request signed URL from backend using individual path components
    const backendResponse = await requestSignedUploadUrlFromBackend(
      storeId,
      folder,
      finalFileName, // Pass the generated filename
      file.type
    );

    const signedUrl = backendResponse.location; // Extract the signed URL from the 'location' field

    // Derive the public URL from the signed URL by removing query parameters
    const publicUrl = signedUrl.split('?')[0];

    // 3. Upload file using the signed URL
    await uploadFileToSignedUrl(file, signedUrl);

    console.log('File upload process completed successfully:', publicUrl);
    return publicUrl;
  } catch (error) {
    console.error('Error orchestrating file upload:', error);
    throw new Error(`Failed to upload file: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Upload multiple files to Firebase Storage via signed URLs.
 * @param files - Array of files to upload
 * @param folder - The folder path
 * @param storeId - The store ID for organizing files
 * @returns Promise<string[]> - Array of public download URLs
 */
export async function uploadMultipleFiles(
  files: File[],
  folder: string,
  storeId: string
): Promise<string[]> {
  try {
    const uploadPromises = files.map(file => uploadFile(file, folder, storeId));
    return await Promise.all(uploadPromises);
  } catch (error) {
    console.error('Error uploading multiple files:', error);
    throw error;
  }
}


// --- Existing Delete and Validation Functions (Unchanged) ---

/**
 * Delete a file from Firebase Storage
 * @param fileUrl - The download URL of the file to delete
 * @param folder - The folder path where the file is stored (can be inferred from URL, but kept for consistency)
 * @param storeId - The store ID for organizing files (can be inferred from URL, but kept for consistency)
 */
export async function deleteFileFromFirebase(fileUrl: string, folder: string, storeId: string): Promise<void> {
  try {
    // Extract the path from the URL
    const url = new URL(fileUrl);
    const pathMatch = url.pathname.match(/\/o\/(.+)$/);
    if (!pathMatch) {
      throw new Error('Invalid Firebase Storage URL');
    }

    // Decode the path
    const fullPath = decodeURIComponent(pathMatch[1]);

    // Create storage reference using the full path
    const storageRef = ref(storage, fullPath);

    // Delete file
    await deleteObject(storageRef);
    console.log('File deleted successfully from Firebase');
  } catch (error) {
    console.error('Error deleting file from Firebase:', error);
    throw error;
  }
}

/**
 * Get file size limit for uploads (10MB default)
 */
export const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB

/**
 * Validate file before upload
 * @param file - File to validate
 * @param allowedTypes - Array of allowed MIME types
 * @param maxSize - Maximum file size in bytes
 */
export function validateFile(
  file: File,
  allowedTypes: string[] = ['image/jpeg', 'image/png', 'image/webp', 'image/gif'],
  maxSize: number = MAX_FILE_SIZE
): { isValid: boolean; error?: string } {
  // Check file type
  if (!allowedTypes.includes(file.type)) {
    return {
      isValid: false,
      error: `File type not allowed. Allowed types: ${allowedTypes.join(', ')}`
    };
  }

  // Check file size
  if (file.size > maxSize) {
    return {
      isValid: false,
      error: `File size too large. Maximum size: ${(maxSize / 1024 / 1024).toFixed(1)}MB`
    };
  }

  return { isValid: true };
}
